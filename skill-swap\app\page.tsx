"use client";

import { useAuth } from "@/context/auth-context";
import Header from "@/components/Header";
import <PERSON> from "@/components/Hero";
import FeaturePreview from "@/components/FeaturePreview";
import Testimonials from "@/components/Testimonials";
import CallToAction from "@/components/CallToAction";
import Footer from "@/components/Footer";
import TopNavBar from "@/components/dashboard/TopNavBar";
import { useState } from "react";
import { Send, MessageSquare, TrendingUp, Users, BookOpen, Award } from "lucide-react";
import { GoogleGenerativeAI } from "@google/generative-ai";

// Initialize Gemini AI (with fallback for missing API key)
let model: any = null;
if (process.env.NEXT_PUBLIC_GEMINI_API_KEY) {
  const genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY);
  model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
}

// Dashboard component for authenticated users
function Dashboard() {
  const { user } = useAuth();
  
  const stats = [
    { label: "Skills Learned", value: "12", icon: BookOpen, color: "text-blue-600" },
    { label: "Skills Taught", value: "8", icon: Users, color: "text-green-600" },
    { label: "Achievements", value: "5", icon: Award, color: "text-purple-600" },
    { label: "Skill Points", value: "2,450", icon: TrendingUp, color: "text-orange-600" }
  ];

  const recentActivity = [
    { action: "Completed React Hooks tutorial", time: "2 hours ago", type: "learning" },
    { action: "Taught Python basics to Alex", time: "1 day ago", type: "teaching" },
    { action: "Earned 'Mentor' badge", time: "3 days ago", type: "achievement" },
    { action: "Started Machine Learning course", time: "1 week ago", type: "learning" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavBar />
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome back, {user?.name || 'SkillSwap User'}! 👋
            </h1>
            <p className="text-gray-600">Ready to learn something new or share your expertise?</p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat) => (
              <div key={stat.label} className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Recent Activity */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.type === 'learning' ? 'bg-blue-500' :
                        activity.type === 'teaching' ? 'bg-green-500' : 'bg-purple-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-[#B10DC9] hover:bg-purple-50 transition-colors">
                    <div className="font-medium text-gray-900">Find a Mentor</div>
                    <div className="text-sm text-gray-500">Connect with experts</div>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-[#B10DC9] hover:bg-purple-50 transition-colors">
                    <div className="font-medium text-gray-900">Offer Your Skills</div>
                    <div className="text-sm text-gray-500">Become a mentor</div>
                  </button>
                  <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-[#B10DC9] hover:bg-purple-50 transition-colors">
                    <div className="font-medium text-gray-900">Browse Courses</div>
                    <div className="text-sm text-gray-500">Explore new skills</div>
                  </button>
                </div>
              </div>

              <div className="bg-gradient-to-br from-[#B10DC9] to-[#8a0a9b] rounded-lg p-6 text-white">
                <h3 className="text-lg font-semibold mb-2">Skill of the Week</h3>
                <p className="text-sm opacity-90 mb-4">
                  React Hooks - Master modern React development with our comprehensive guide.
                </p>
                <button className="bg-white text-[#B10DC9] px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                  Start Learning
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  const { isAuthenticated, loading } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [input, setInput] = useState("");
  const [chatMessages, setChatMessages] = useState<{ sender: string; text: string }[]>([]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!input.trim()) return;

    const userMessage = { sender: "user", text: input };
    setChatMessages([...chatMessages, userMessage]);
    setInput("");

    try {
      setChatMessages((prevMessages) => [
        ...prevMessages,
        { sender: "bot", text: "..." },
      ]);

      if (model) {
        const result = await model.generateContentStream([input]);
        let botResponse = "";
        for await (const chunk of result.stream) {
          botResponse += chunk.text();
        }
        setChatMessages((prevMessages) => [
          ...prevMessages.slice(0, -1),
          { sender: "bot", text: botResponse || "Sorry, I couldn't understand." },
        ]);
      } else {
        // Fallback response when API key is not available
        setChatMessages((prevMessages) => [
          ...prevMessages.slice(0, -1),
          { sender: "bot", text: "Hi! I'm the SwapUP assistant. The AI features are currently in demo mode. How can I help you with skill swapping today?" },
        ]);
      }
    } catch (error) {
      console.error("Error fetching response:", error);
      setChatMessages((prevMessages) => [
        ...prevMessages.slice(0, -1),
        { sender: "bot", text: "There was an error processing your request. Please try again." },
      ]);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#B10DC9]"></div>
      </div>
    );
  }

  // Show dashboard for authenticated users, landing page for others
  if (isAuthenticated) {
    return (
      <>
        <Dashboard />
        {/* Chatbot */}
        <div className="fixed bottom-4 right-4">
          {isOpen ? (
            <div className="w-96 bg-white shadow-xl rounded-lg overflow-hidden border border-gray-200">
              {/* Header */}
              <div className="bg-[#B10DC9] text-white p-3 flex items-center justify-between">
                <button
                  className="hover:bg-[#960BB0] p-1 rounded-full transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <span className="font-semibold">SwapUP Assistant</span>
                <div className="w-6" />
              </div>

              {/* Chat Messages */}
              <div className="h-[400px] overflow-y-auto p-4 space-y-4 bg-gray-50">
                {chatMessages.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex ${
                      msg.sender === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`max-w-[80%] px-4 py-2 rounded-lg ${
                        msg.sender === "user"
                          ? "bg-[#B10DC9] text-white rounded-br-none"
                          : "bg-gray-200 text-gray-900 rounded-bl-none"
                      }`}
                    >
                      {msg.text}
                    </div>
                  </div>
                ))}
              </div>

              {/* Input Area */}
              <div className="p-3 bg-white border-t border-gray-200">
                <div className="flex gap-2">
                  <input
                    type="text"
                    className="flex-1 px-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:border-[#B10DC9]"
                    placeholder="Ask me anything..."
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <button
                    className="bg-[#B10DC9] hover:bg-[#960BB0] text-white p-2 rounded-full transition-colors"
                    onClick={handleSendMessage}
                  >
                    <Send size={20} />
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <button
              className="bg-[#B10DC9] hover:bg-[#960BB0] text-white p-4 rounded-full shadow-lg transition-colors"
              onClick={() => setIsOpen(true)}
            >
              <MessageSquare size={24} />
            </button>
          )}
        </div>
      </>
    );
  }

  // Landing page for non-authenticated users
  return (
    <>
      <Header />
      <main className="flex min-h-screen flex-col">
        <Hero />
        <FeaturePreview />
        <Testimonials />
        <CallToAction />

        {/* Chatbot for non-authenticated users */}
        <div className="fixed bottom-4 right-4">
          {isOpen ? (
            <div className="w-96 bg-white shadow-xl rounded-lg overflow-hidden border border-gray-200">
              <div className="bg-[#B10DC9] text-white p-3 flex items-center justify-between">
                <button
                  className="hover:bg-[#960BB0] p-1 rounded-full transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <span className="font-semibold">SwapUP Assistant</span>
                <div className="w-6" />
              </div>

              <div className="h-[400px] overflow-y-auto p-4 space-y-4 bg-gray-50">
                {chatMessages.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex ${
                      msg.sender === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`max-w-[80%] px-4 py-2 rounded-lg ${
                        msg.sender === "user"
                          ? "bg-[#B10DC9] text-white rounded-br-none"
                          : "bg-gray-200 text-gray-900 rounded-bl-none"
                      }`}
                    >
                      {msg.text}
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-3 bg-white border-t border-gray-200">
                <div className="flex gap-2">
                  <input
                    type="text"
                    className="flex-1 px-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:border-[#B10DC9]"
                    placeholder="Ask me anything..."
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <button
                    className="bg-[#B10DC9] hover:bg-[#960BB0] text-white p-2 rounded-full transition-colors"
                    onClick={handleSendMessage}
                  >
                    <Send size={20} />
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <button
              className="bg-[#B10DC9] hover:bg-[#960BB0] text-white p-4 rounded-full shadow-lg transition-colors"
              onClick={() => setIsOpen(true)}
            >
              <MessageSquare size={24} />
            </button>
          )}
        </div>
      </main>
      <Footer />
    </>
  );
}
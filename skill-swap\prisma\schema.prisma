// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  avatar    String?
  bio       String?
  location  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  skills           UserSkill[]
  sentMessages     Message[] @relation("MessageSender")
  receivedMessages Message[] @relation("MessageReceiver")
  posts            Post[]
  likes            Like[]
  comments         Comment[]

  @@map("users")
}

model Skill {
  id          String @id @default(cuid())
  name        String @unique
  category    String
  description String?
  icon        String?

  // Relations
  userSkills UserSkill[]

  @@map("skills")
}

model UserSkill {
  id           String @id @default(cuid())
  userId       String
  skillId      String
  level        String // beginner, intermediate, advanced, expert
  isTeaching   <PERSON><PERSON><PERSON> @default(false)
  isLearning   <PERSON><PERSON><PERSON> @default(false)
  hourlyRate   Float?
  availability String?

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  skill Skill @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@unique([userId, skillId])
  @@map("user_skills")
}

model Message {
  id         String   @id @default(cuid())
  content    String
  senderId   String
  receiverId String
  createdAt  DateTime @default(now())
  read       Boolean  @default(false)

  // Relations
  sender   User @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Post {
  id        String   @id @default(cuid())
  content   String
  imageUrl  String?
  videoUrl  String?
  type      String   @default("text") // text, image, video, reel
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  likes    Like[]
  comments Comment[]

  @@map("posts")
}

model Like {
  id     String @id @default(cuid())
  userId String
  postId String

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@map("likes")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  userId    String
  postId    String
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@map("comments")
}
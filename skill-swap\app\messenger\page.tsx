"use client"

import ProtectedRoute from "@/components/ProtectedRoute"
import TopNavBar from "@/components/dashboard/TopNavBar"
import { MessageCircle, Send, Search } from "lucide-react"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"

const mockConversations = [
  {
    id: 1,
    name: "<PERSON>",
    lastMessage: "Thanks for the React tutorial!",
    time: "2m ago",
    unread: 2,
    avatar: "AT"
  },
  {
    id: 2,
    name: "<PERSON>",
    lastMessage: "When can we schedule the design session?",
    time: "1h ago",
    unread: 0,
    avatar: "SC"
  },
  {
    id: 3,
    name: "<PERSON>",
    lastMessage: "The Python code works perfectly",
    time: "3h ago",
    unread: 1,
    avatar: "MR"
  }
]

export default function MessengerPage() {
  const [selectedConversation, setSelectedConversation] = useState(mockConversations[0])
  const [message, setMessage] = useState("")

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim()) {
      // Handle sending message
      console.log("Sending message:", message)
      setMessage("")
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <TopNavBar />
        <div className="pt-16 h-screen flex">
          {/* Conversations List */}
          <div className="w-1/3 bg-white border-r border-gray-200">
            <div className="p-4 border-b border-gray-200">
              <h1 className="text-xl font-semibold mb-4">Messages</h1>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search conversations..."
                  className="pl-10"
                />
              </div>
            </div>
            <div className="overflow-y-auto">
              {mockConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => setSelectedConversation(conversation)}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                    selectedConversation.id === conversation.id ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-[#B10DC9] rounded-full flex items-center justify-center text-white font-semibold">
                      {conversation.avatar}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {conversation.name}
                        </p>
                        <p className="text-xs text-gray-500">{conversation.time}</p>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-500 truncate">
                          {conversation.lastMessage}
                        </p>
                        {conversation.unread > 0 && (
                          <span className="bg-[#B10DC9] text-white text-xs rounded-full px-2 py-1">
                            {conversation.unread}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Chat Header */}
            <div className="bg-white border-b border-gray-200 p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-[#B10DC9] rounded-full flex items-center justify-center text-white font-semibold">
                  {selectedConversation.avatar}
                </div>
                <div>
                  <h2 className="text-lg font-semibold">{selectedConversation.name}</h2>
                  <p className="text-sm text-gray-500">Online</p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              <div className="flex justify-start">
                <div className="bg-gray-200 rounded-lg px-4 py-2 max-w-xs">
                  <p className="text-sm">Hey! I'd love to learn React from you. When are you available?</p>
                  <p className="text-xs text-gray-500 mt-1">10:30 AM</p>
                </div>
              </div>
              <div className="flex justify-end">
                <div className="bg-[#B10DC9] text-white rounded-lg px-4 py-2 max-w-xs">
                  <p className="text-sm">Hi Alex! I'm free this weekend. We can start with the basics.</p>
                  <p className="text-xs text-purple-200 mt-1">10:32 AM</p>
                </div>
              </div>
              <div className="flex justify-start">
                <div className="bg-gray-200 rounded-lg px-4 py-2 max-w-xs">
                  <p className="text-sm">{selectedConversation.lastMessage}</p>
                  <p className="text-xs text-gray-500 mt-1">10:35 AM</p>
                </div>
              </div>
            </div>

            {/* Message Input */}
            <form onSubmit={handleSendMessage} className="bg-white border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1"
                />
                <Button type="submit" className="bg-[#B10DC9] hover:bg-[#8a0a9b]">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
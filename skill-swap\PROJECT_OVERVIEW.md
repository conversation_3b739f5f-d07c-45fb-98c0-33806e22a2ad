# SwapUP - Project Overview & Architecture

## 🎯 Project Vision

SwapUP is a modern skill-sharing platform that connects learners with experts, enabling knowledge exchange through various interactive mediums including messaging, video content, and real-time collaboration.

## 🏗️ Architecture Overview

### Technology Stack

#### Frontend
- **Next.js 14.2.16** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component library
- **Lucide React** - Icon library

#### Backend
- **Next.js API Routes** - Serverless API endpoints
- **Prisma ORM** - Database toolkit
- **SQLite** - Development database (PostgreSQL for production)
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing

#### Development Tools
- **ESLint** - Code linting
- **TypeScript** - Static type checking
- **Prisma Studio** - Database GUI

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Layer     │    │   Database      │
│   (Next.js)     │◄──►│   (API Routes)  │◄──►│   (SQLite)      │
│                 │    │                 │    │                 │
│ • React Pages   │    │ • Authentication│    │ • User Data     │
│ • Components    │    │ • CRUD Ops      │    │ • Skills        │
│ • Context       │    │ • Business Logic│    │ • Messages      │
│ • Hooks         │    │ • Validation    │    │ • Posts         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Database Schema

### Core Entities

#### User Management
```sql
User {
  id: String (Primary Key)
  email: String (Unique)
  name: String
  password: String (Hashed)
  avatar: String?
  bio: String?
  location: String?
  createdAt: DateTime
  updatedAt: DateTime
}
```

#### Skill System
```sql
Skill {
  id: String (Primary Key)
  name: String (Unique)
  category: String
  description: String?
  icon: String?
}

UserSkill {
  id: String (Primary Key)
  userId: String (Foreign Key)
  skillId: String (Foreign Key)
  level: String (beginner|intermediate|advanced|expert)
  isTeaching: Boolean
  isLearning: Boolean
  hourlyRate: Float?
  availability: String?
}
```

#### Communication
```sql
Message {
  id: String (Primary Key)
  content: String
  senderId: String (Foreign Key)
  receiverId: String (Foreign Key)
  createdAt: DateTime
  read: Boolean
}
```

#### Content Sharing
```sql
Post {
  id: String (Primary Key)
  content: String
  imageUrl: String?
  videoUrl: String?
  type: String (text|image|video|reel)
  userId: String (Foreign Key)
  createdAt: DateTime
  updatedAt: DateTime
}

Like {
  id: String (Primary Key)
  userId: String (Foreign Key)
  postId: String (Foreign Key)
}

Comment {
  id: String (Primary Key)
  content: String
  userId: String (Foreign Key)
  postId: String (Foreign Key)
  createdAt: DateTime
}
```

## 🔐 Authentication Flow

### Registration Process
1. User submits registration form
2. Server validates input data
3. Password is hashed using bcrypt
4. User record created in database
5. JWT token generated and returned
6. Token stored in localStorage
7. User redirected to dashboard

### Login Process
1. User submits credentials
2. Server validates email/password
3. Password verified against hash
4. JWT token generated with user data
5. Token returned to client
6. Token stored in localStorage
7. User redirected to dashboard

### Route Protection
1. Protected routes wrapped with `ProtectedRoute` component
2. Component checks for valid JWT in localStorage
3. Token verified on server side
4. Invalid/expired tokens trigger logout
5. Unauthenticated users redirected to login

## 🎨 UI/UX Design System

### Color Palette
```css
Primary: #B10DC9 (Purple)
Secondary: #4F46E5 (Indigo)
Success: #10B981 (Green)
Warning: #F59E0B (Amber)
Error: #EF4444 (Red)
Gray Scale: #F9FAFB to #111827
```

### Typography
```css
Font Family: Inter (System fallback)
Headings: 
  - H1: 3rem (48px) - font-bold
  - H2: 2.25rem (36px) - font-semibold
  - H3: 1.875rem (30px) - font-semibold
  - H4: 1.5rem (24px) - font-medium

Body Text: 1rem (16px) - font-normal
Small Text: 0.875rem (14px) - font-normal
```

### Component Hierarchy
```
Layout Components:
├── Header/TopNavBar
├── PublicNavBar
├── Footer
└── ClientLayout

Page Components:
├── Dashboard
├── Login/Signup
├── Messenger
├── Reels
├── Explore
└── Search

UI Components:
├── Button
├── Input
├── Select
├── Modal/Dialog
├── Card
└── Badge
```

## 🔄 State Management

### Authentication State
- **Context**: `AuthContext`
- **Provider**: `AuthProvider`
- **Hook**: `useAuth()`
- **Storage**: localStorage for persistence

### Component State
- **Local State**: useState for component-specific data
- **Side Effects**: useEffect for API calls and subscriptions
- **Memoization**: useMemo/useCallback for performance

### Data Flow
```
User Action → Component State → API Call → Database → Response → State Update → UI Re-render
```

## 🛣️ Routing Structure

### Public Routes
```
/ - Landing page (shows dashboard if authenticated)
/login - User authentication
/signup - User registration
```

### Protected Routes
```
/dashboard - User dashboard (same as authenticated /)
/messenger - Real-time messaging
/reels - Video content sharing
/explore - Skill discovery
/search - Advanced user search
```

### API Routes
```
/api/auth/login - POST - User authentication
/api/auth/signup - POST - User registration
/api/users - GET/POST/PUT/DELETE - User management
/api/skills - GET/POST/PUT/DELETE - Skill management
/api/messages - GET/POST - Messaging
/api/posts - GET/POST/PUT/DELETE - Content management
```

## 🔧 Development Workflow

### File Organization
```
app/
├── (auth)/          # Authentication pages
├── (dashboard)/     # Protected dashboard pages
├── api/            # API endpoints
├── globals.css     # Global styles
├── layout.tsx      # Root layout
└── page.tsx        # Home page

components/
├── ui/             # Reusable UI components
├── dashboard/      # Dashboard-specific components
├── forms/          # Form components
└── layout/         # Layout components

lib/
├── auth.ts         # Authentication utilities
├── prisma.ts       # Database client
├── utils.ts        # General utilities
└── validations.ts  # Input validation schemas

context/
└── auth-context.tsx # Authentication context
```

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration
- **Naming**: camelCase for variables, PascalCase for components
- **File Naming**: kebab-case for files, PascalCase for components
- **Imports**: Absolute imports using @ alias

### Git Workflow
```
main (production)
├── develop (integration)
│   ├── feature/user-authentication
│   ├── feature/messaging-system
│   └── feature/video-reels
└── hotfix/critical-bug-fix
```

## 🚀 Deployment Strategy

### Development Environment
- **Database**: SQLite file-based
- **Authentication**: Dummy credentials enabled
- **API Keys**: Optional (fallback to dummy responses)
- **Caching**: Disabled for development

### Production Environment
- **Database**: PostgreSQL or MySQL
- **Authentication**: Secure JWT secrets
- **API Keys**: Required for external services
- **Caching**: Redis for session management
- **CDN**: For static assets

### CI/CD Pipeline
```
Code Push → GitHub Actions → Tests → Build → Deploy → Health Check
```

## 📈 Performance Considerations

### Frontend Optimization
- **Code Splitting**: Automatic with Next.js App Router
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: @next/bundle-analyzer
- **Lazy Loading**: Dynamic imports for heavy components

### Backend Optimization
- **Database Indexing**: Proper indexes on frequently queried fields
- **Query Optimization**: Efficient Prisma queries with includes
- **Caching**: API response caching
- **Rate Limiting**: Prevent API abuse

### Monitoring
- **Error Tracking**: Sentry or similar
- **Performance Monitoring**: Web Vitals
- **Database Monitoring**: Query performance
- **User Analytics**: Usage patterns and bottlenecks

## 🔒 Security Measures

### Authentication Security
- **Password Hashing**: bcrypt with salt rounds
- **JWT Security**: Secure secrets, proper expiration
- **Token Storage**: HttpOnly cookies (future enhancement)
- **Session Management**: Automatic logout on token expiry

### Data Protection
- **Input Validation**: Zod schemas for all inputs
- **SQL Injection**: Prisma ORM protection
- **XSS Prevention**: React built-in protections
- **CSRF Protection**: SameSite cookies

### API Security
- **Rate Limiting**: Prevent brute force attacks
- **CORS Configuration**: Proper origin restrictions
- **Input Sanitization**: Clean all user inputs
- **Error Handling**: No sensitive data in error messages

## 🧪 Testing Strategy

### Unit Testing
- **Components**: React Testing Library
- **Utilities**: Jest
- **API Routes**: Supertest
- **Database**: In-memory SQLite

### Integration Testing
- **User Flows**: Playwright or Cypress
- **API Integration**: Full request/response cycle
- **Database Integration**: Real database operations

### Performance Testing
- **Load Testing**: Artillery or k6
- **Bundle Size**: Bundle analyzer
- **Lighthouse**: Core Web Vitals

## 📚 Documentation

### Code Documentation
- **JSDoc**: Function and component documentation
- **README**: Setup and usage instructions
- **API Docs**: OpenAPI/Swagger specifications
- **Architecture**: System design documents

### User Documentation
- **User Guide**: Feature explanations
- **FAQ**: Common questions and answers
- **Video Tutorials**: Screen recordings
- **Help Center**: Searchable knowledge base

## 🔮 Future Enhancements

### Phase 2 Features
- **Real-time Messaging**: WebSocket implementation
- **Video Calls**: WebRTC integration
- **Payment System**: Stripe integration
- **Mobile App**: React Native version

### Phase 3 Features
- **AI Recommendations**: Machine learning for skill matching
- **Gamification**: Points, badges, leaderboards
- **Group Learning**: Virtual classrooms
- **Certification**: Skill verification system

### Technical Improvements
- **Microservices**: Service decomposition
- **GraphQL**: API layer enhancement
- **PWA**: Progressive Web App features
- **Internationalization**: Multi-language support

---

**This project represents a comprehensive skill-sharing platform built with modern web technologies and best practices. The architecture is designed to be scalable, maintainable, and user-friendly.**
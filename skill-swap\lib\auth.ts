import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { prisma } from './prisma'

const JWT_SECRET = process.env.JWT_SECRET || 'dummy-secret-key-for-development'

export interface User {
  id: string
  email: string
  name: string
  avatar?: string | null
}

export function generateToken(user: User): string {
  return jwt.sign(
    { 
      id: user.id, 
      email: user.email, 
      name: user.name 
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  )
}

export function verifyToken(token: string): User | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    return {
      id: decoded.id,
      email: decoded.email,
      name: decoded.name,
      avatar: decoded.avatar
    }
  } catch (error) {
    return null
  }
}

export async function validateCredentials(email: string, password: string): Promise<User | null> {
  try {
    // First check if it's the dummy credentials
    if (email === '<EMAIL>' && password === 'dummyPass') {
      // Check if dummy user exists in database, if not create it
      let user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      })

      if (!user) {
        const hashedPassword = await bcrypt.hash('dummyPass', 10)
        user = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'SkillSwap User',
            password: hashedPassword
          }
        })
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar
      }
    }

    // Check database for real users
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      return null
    }

    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar
    }
  } catch (error) {
    console.error('Error validating credentials:', error)
    return null
  }
}

export async function createUser(email: string, password: string, name: string): Promise<User | null> {
  try {
    const hashedPassword = await bcrypt.hash(password, 10)
    
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name
      }
    })

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar
    }
  } catch (error) {
    console.error('Error creating user:', error)
    return null
  }
}
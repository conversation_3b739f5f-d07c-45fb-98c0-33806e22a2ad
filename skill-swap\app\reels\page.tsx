"use client"

import ProtectedRoute from "@/components/ProtectedRoute"
import TopNavBar from "@/components/dashboard/TopNavBar"
import { Play, Heart, MessageCircle, Share, MoreVertical } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

const mockReels = [
  {
    id: 1,
    title: "React Hooks Explained in 60 Seconds",
    author: "<PERSON>",
    likes: 1234,
    comments: 89,
    shares: 45,
    thumbnail: "https://via.placeholder.com/300x400/B10DC9/white?text=React+Hooks",
    duration: "0:58"
  },
  {
    id: 2,
    title: "Python Data Structures Quick Guide",
    author: "<PERSON>",
    likes: 987,
    comments: 67,
    shares: 32,
    thumbnail: "https://via.placeholder.com/300x400/4F46E5/white?text=Python+DS",
    duration: "1:23"
  },
  {
    id: 3,
    title: "UI/UX Design Principles",
    author: "<PERSON>",
    likes: 2156,
    comments: 134,
    shares: 78,
    thumbnail: "https://via.placeholder.com/300x400/10B981/white?text=UI%2FUX",
    duration: "2:15"
  },
  {
    id: 4,
    title: "JavaScript ES6 Features",
    author: "<PERSON>",
    likes: 1567,
    comments: 92,
    shares: 56,
    thumbnail: "https://via.placeholder.com/300x400/F59E0B/white?text=JS+ES6",
    duration: "1:45"
  }
]

export default function ReelsPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <TopNavBar />
        <div className="pt-16">
          <div className="max-w-6xl mx-auto px-4 py-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Skill Reels</h1>
              <p className="text-gray-600">Quick skill tutorials and tips from our community</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {mockReels.map((reel) => (
                <div key={reel.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={reel.thumbnail}
                      alt={reel.title}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                      <Button size="lg" className="rounded-full bg-white bg-opacity-90 text-gray-900 hover:bg-white">
                        <Play className="h-6 w-6" />
                      </Button>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                      {reel.duration}
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {reel.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">by {reel.author}</p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Heart className="h-4 w-4" />
                          <span>{reel.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="h-4 w-4" />
                          <span>{reel.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Share className="h-4 w-4" />
                          <span>{reel.shares}</span>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Create Reel Button */}
            <div className="fixed bottom-8 right-8">
              <Button className="rounded-full bg-[#B10DC9] hover:bg-[#8a0a9b] text-white shadow-lg h-14 w-14">
                <Play className="h-6 w-6" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
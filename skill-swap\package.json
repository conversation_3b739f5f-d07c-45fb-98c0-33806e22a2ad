{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "ml-service": "cd ml_service && .\\venv\\Scripts\\python.exe -m uvicorn main:app --reload --port 8000", "dev-with-ml": "concurrently \"npm run ml-service\" \"npm run dev\"", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.15.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cors": "^2.8.5", "date-fns": "^3.3.1", "embla-carousel-react": "8.5.1", "express": "^5.1.0", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "14.2.16", "next-themes": "^0.4.4", "playwright": "^1.5.0", "prisma": "^6.15.0", "react": "^18", "react-chartjs-2": "^5.3.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "concurrently": "^9.2.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}
-- SkillSwap Database Setup
CREATE DATABASE IF NOT EXISTS skillswap;
USE skillswap;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHA<PERSON>(100),
    bio TEXT,
    profile_picture VARCHAR(255),
    location VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Skills table
CREATE TABLE IF NOT EXISTS skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User skills table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    skill_id INT NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'beginner',
    is_teaching BOOLEAN DEFAULT FALSE,
    is_learning BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_skill (user_id, skill_id)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Skill matches table
CREATE TABLE IF NOT EXISTS skill_matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT NOT NULL,
    learner_id INT NOT NULL,
    skill_id INT NOT NULL,
    status ENUM('pending', 'accepted', 'rejected', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (learner_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
);

-- Reels table
CREATE TABLE IF NOT EXISTS reels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    skill_id INT,
    likes_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE SET NULL
);

-- Reel likes table
CREATE TABLE IF NOT EXISTS reel_likes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    reel_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reel_id) REFERENCES reels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_reel_like (user_id, reel_id)
);

-- Insert sample skills
INSERT IGNORE INTO skills (name, category, description) VALUES
('JavaScript', 'Programming', 'Popular programming language for web development'),
('Python', 'Programming', 'Versatile programming language for various applications'),
('React', 'Web Development', 'JavaScript library for building user interfaces'),
('Node.js', 'Backend Development', 'JavaScript runtime for server-side development'),
('MySQL', 'Database', 'Relational database management system'),
('PHP', 'Backend Development', 'Server-side scripting language'),
('HTML/CSS', 'Web Development', 'Markup and styling languages for web pages'),
('Machine Learning', 'Data Science', 'AI technique for pattern recognition and prediction'),
('Graphic Design', 'Design', 'Visual communication and design skills'),
('Digital Marketing', 'Marketing', 'Online marketing and promotion strategies'),
('Project Management', 'Management', 'Planning and executing projects effectively'),
('Public Speaking', 'Communication', 'Effective presentation and speaking skills'),
('Photography', 'Creative', 'Art and technique of capturing images'),
('Video Editing', 'Creative', 'Post-production video processing and editing'),
('Data Analysis', 'Data Science', 'Analyzing and interpreting data for insights');

-- Insert sample users
INSERT IGNORE INTO users (username, email, password, full_name, bio, location) VALUES
('john_doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', 'Full-stack developer passionate about teaching web technologies', 'New York, NY'),
('jane_smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Smith', 'Data scientist and machine learning enthusiast', 'San Francisco, CA'),
('mike_wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike Wilson', 'Graphic designer with 5+ years of experience', 'Los Angeles, CA'),
('sarah_johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah Johnson', 'Marketing professional and content creator', 'Chicago, IL');

-- Insert sample user skills
INSERT IGNORE INTO user_skills (user_id, skill_id, proficiency_level, is_teaching, is_learning) VALUES
(1, 1, 'expert', TRUE, FALSE),    -- John teaches JavaScript
(1, 3, 'advanced', TRUE, FALSE),  -- John teaches React
(1, 4, 'advanced', TRUE, FALSE),  -- John teaches Node.js
(2, 2, 'expert', TRUE, FALSE),    -- Jane teaches Python
(2, 8, 'advanced', TRUE, FALSE),  -- Jane teaches Machine Learning
(2, 15, 'advanced', TRUE, FALSE), -- Jane teaches Data Analysis
(3, 9, 'expert', TRUE, FALSE),    -- Mike teaches Graphic Design
(3, 13, 'advanced', TRUE, FALSE), -- Mike teaches Photography
(4, 10, 'advanced', TRUE, FALSE), -- Sarah teaches Digital Marketing
(4, 12, 'advanced', TRUE, FALSE), -- Sarah teaches Public Speaking
(1, 8, 'beginner', FALSE, TRUE),  -- John wants to learn Machine Learning
(2, 9, 'beginner', FALSE, TRUE),  -- Jane wants to learn Graphic Design
(3, 2, 'intermediate', FALSE, TRUE), -- Mike wants to learn Python
(4, 3, 'beginner', FALSE, TRUE);  -- Sarah wants to learn React

-- Insert sample reels
INSERT IGNORE INTO reels (user_id, title, description, video_url, thumbnail_url, skill_id) VALUES
(1, 'JavaScript Fundamentals: Variables and Functions', 'Learn the basics of JavaScript programming', 'https://example.com/video1.mp4', 'https://example.com/thumb1.jpg', 1),
(2, 'Introduction to Machine Learning with Python', 'Get started with ML using Python libraries', 'https://example.com/video2.mp4', 'https://example.com/thumb2.jpg', 8),
(3, 'Logo Design Principles', 'Essential principles for creating effective logos', 'https://example.com/video3.mp4', 'https://example.com/thumb3.jpg', 9),
(4, 'Social Media Marketing Strategy', 'Build an effective social media presence', 'https://example.com/video4.mp4', 'https://example.com/thumb4.jpg', 10);

"use client"

import ProtectedRoute from "@/components/ProtectedRoute"
import TopNavBar from "@/components/dashboard/TopNavBar"
import { Search, Filter, Star, MapPin, Clock, TrendingUp } from "lucide-react"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const trendingSkills = [
  { name: "React", growth: "+25%", color: "bg-blue-100 text-blue-800" },
  { name: "Python", growth: "+18%", color: "bg-green-100 text-green-800" },
  { name: "UI/UX Design", growth: "+32%", color: "bg-purple-100 text-purple-800" },
  { name: "Machine Learning", growth: "+45%", color: "bg-red-100 text-red-800" },
  { name: "Node.js", growth: "+22%", color: "bg-yellow-100 text-yellow-800" },
  { name: "Flutter", growth: "+38%", color: "bg-indigo-100 text-indigo-800" }
]

const featuredExperts = [
  {
    id: 1,
    name: "<PERSON>. <PERSON>",
    expertise: "AI & Machine Learning",
    rating: 4.9,
    students: 2500,
    location: "San Francisco, CA",
    hourlyRate: "$75-100",
    avatar: "SJ",
    skills: ["TensorFlow", "PyTorch", "Deep Learning"]
  },
  {
    id: 2,
    name: "Marcus Chen",
    expertise: "Full Stack Development",
    rating: 4.8,
    students: 1800,
    location: "Toronto, Canada",
    hourlyRate: "$60-80",
    avatar: "MC",
    skills: ["React", "Node.js", "MongoDB"]
  },
  {
    id: 3,
    name: "Elena Rodriguez",
    expertise: "Product Design",
    rating: 4.9,
    students: 1200,
    location: "Barcelona, Spain",
    hourlyRate: "$55-75",
    avatar: "ER",
    skills: ["Figma", "Prototyping", "User Research"]
  }
]

const categories = [
  { name: "Programming", count: 1250, icon: "💻" },
  { name: "Design", count: 890, icon: "🎨" },
  { name: "Business", count: 650, icon: "📊" },
  { name: "Marketing", count: 420, icon: "📈" },
  { name: "Data Science", count: 380, icon: "📊" },
  { name: "Photography", count: 290, icon: "📸" }
]

export default function ExplorePage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <TopNavBar />
        <div className="pt-16">
          <div className="max-w-7xl mx-auto px-4 py-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Explore Skills</h1>
              <p className="text-gray-600">Discover new skills and connect with expert instructors</p>
            </div>

            {/* Search and Filters */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Search for skills, instructors, or topics..."
                    className="pl-10"
                  />
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-8">
                {/* Trending Skills */}
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <TrendingUp className="h-5 w-5 text-[#B10DC9]" />
                    <h2 className="text-xl font-semibold">Trending Skills</h2>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {trendingSkills.map((skill) => (
                      <div key={skill.name} className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{skill.name}</h3>
                          <Badge className={skill.color}>{skill.growth}</Badge>
                        </div>
                        <p className="text-sm text-gray-500">High demand</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Featured Experts */}
                <div>
                  <h2 className="text-xl font-semibold mb-4">Featured Experts</h2>
                  <div className="space-y-4">
                    {featuredExperts.map((expert) => (
                      <div key={expert.id} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 bg-[#B10DC9] rounded-full flex items-center justify-center text-white font-bold text-lg">
                            {expert.avatar}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h3 className="text-lg font-semibold">{expert.name}</h3>
                                <p className="text-gray-600">{expert.expertise}</p>
                              </div>
                              <div className="text-right">
                                <div className="flex items-center gap-1 mb-1">
                                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                  <span className="font-medium">{expert.rating}</span>
                                </div>
                                <p className="text-sm text-gray-500">{expert.students} students</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                              <div className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                {expert.location}
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {expert.hourlyRate}/hour
                              </div>
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {expert.skills.map((skill) => (
                                <Badge key={skill} variant="secondary" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Categories */}
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-4">Browse Categories</h3>
                  <div className="space-y-3">
                    {categories.map((category) => (
                      <div key={category.name} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <div className="flex items-center gap-3">
                          <span className="text-xl">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <Badge variant="secondary">{category.count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold mb-4">Platform Stats</h3>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#B10DC9]">15,000+</div>
                      <div className="text-sm text-gray-500">Active Instructors</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#B10DC9]">50,000+</div>
                      <div className="text-sm text-gray-500">Students Learning</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#B10DC9]">200+</div>
                      <div className="text-sm text-gray-500">Skill Categories</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
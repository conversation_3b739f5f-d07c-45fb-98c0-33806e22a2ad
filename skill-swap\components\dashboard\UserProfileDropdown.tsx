"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { User, Settings, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/context/auth-context"
import { useRouter } from "next/navigation"

interface UserProfileDropdownProps {
  userName: string
  userAvatar: string
}

export default function UserProfileDropdown({ userName, userAvatar }: UserProfileDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const { logout } = useAuth()
  const router = useRouter()

  const toggleDropdown = () => setIsOpen(!isOpen)

  const handleLogout = () => {
    logout()
    router.push('/login')
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <Button onClick={toggleDropdown} variant="ghost" className="flex items-center space-x-2 focus:outline-none">
        <div className="w-8 h-8 bg-[#B10DC9] rounded-full flex items-center justify-center text-white font-semibold text-sm">
          {userName.charAt(0).toUpperCase()}
        </div>
        <span className="font-medium text-sm">{userName}</span>
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200">
          <div className="px-4 py-2 border-b border-gray-200">
            <p className="text-sm font-medium text-gray-900">{userName}</p>
          </div>

          <Link href="/dashboard" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <User className="h-4 w-4 mr-2" />
            Dashboard
          </Link>
          <Link href="/dashboard/matches" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <User className="h-4 w-4 mr-2" />
            Matches
          </Link>
          <Link href="/dashboard/settings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Link>

          <div className="border-t border-gray-200">
            <button 
              onClick={handleLogout}
              className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
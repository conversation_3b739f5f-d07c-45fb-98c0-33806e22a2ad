"use client"

import { useState } from "react"
import { Bell, <PERSON> } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import UserProfileDropdown from "./UserProfileDropdown"
import { useAuth } from "@/context/auth-context"

type Notification = {
  id: string
  title: string
  message: string
  time: string
  read: boolean
}

export default function TopNavBar() {
  const router = useRouter()
  const { isAuthenticated, user, logout } = useAuth()
  const userName = user?.name || "SkillSwap User"
  const userAvatar = "/placeholder.svg?height=32&width=32"

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      title: "New Match Request",
      message: "<PERSON> wants to learn React from you",
      time: "5m ago",
      read: false
    },
    {
      id: "2",
      title: "Achievement Unlocked",
      message: "You've earned the '<PERSON>tor' badge!",
      time: "1h ago",
      read: false
    }
  ])

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, read: true } : n
    ))
  }

  const handleLogout = () => {
    logout()
    router.push('/login')
  }

  return (
    <nav className="bg-white shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <span className="text-2xl font-bold text-[#B10DC9]">SwapUP</span>
            </Link>
          </div>
          
          {/* Search Bar - Only show when authenticated */}
          {isAuthenticated && (
            <div className="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-end">
              <div className="max-w-lg w-full lg:max-w-xs">
                <label htmlFor="search" className="sr-only">
                  Search
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    id="search"
                    name="search"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#B10DC9] focus:border-[#B10DC9] sm:text-sm"
                    placeholder="Search for skills or users"
                    type="search"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Navigation Links */}
          <div className="hidden lg:ml-6 lg:flex lg:items-center space-x-8">
            <Link 
              href="/" 
              className="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium"
            >
              Home
            </Link>
            {isAuthenticated ? (
              <>
                <Link 
                  href="/messenger" 
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium"
                >
                  Messenger
                </Link>
                <Link 
                  href="/reels" 
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium"
                >
                  Reels
                </Link>
                <Link 
                  href="/explore" 
                  className="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium"
                >
                  Explore
                </Link>
              </>
            ) : (
              <Link 
                href="/login" 
                className="bg-[#B10DC9] text-white hover:bg-[#8a0a9b] px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Login
              </Link>
            )}
          </div>

          {/* Right side icons - Only show when authenticated */}
          {isAuthenticated && (
            <div className="flex items-center ml-4">
              {/* Notifications */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="relative mr-2">
                    <Bell className="h-6 w-6" />
                    {unreadCount > 0 && (
                      <Badge 
                        className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500 text-white text-xs"
                      >
                        {unreadCount}
                      </Badge>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-80">
                  <div className="flex items-center justify-between px-4 py-2 border-b">
                    <span className="font-semibold">Notifications</span>
                    {unreadCount > 0 && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setNotifications(notifications.map(n => ({ ...n, read: true })))}
                      >
                        Mark all as read
                      </Button>
                    )}
                  </div>
                  <div className="max-h-[300px] overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.map((notification) => (
                        <DropdownMenuItem
                          key={notification.id}
                          className={`px-4 py-2 cursor-pointer ${!notification.read ? 'bg-muted/50' : ''}`}
                          onClick={() => markAsRead(notification.id)}
                        >
                          <div>
                            <div className="font-medium">{notification.title}</div>
                            <div className="text-sm text-muted-foreground">{notification.message}</div>
                            <div className="text-xs text-muted-foreground mt-1">{notification.time}</div>
                          </div>
                        </DropdownMenuItem>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-center text-muted-foreground">
                        No notifications
                      </div>
                    )}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <UserProfileDropdown userName={userName} userAvatar={userAvatar} />
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
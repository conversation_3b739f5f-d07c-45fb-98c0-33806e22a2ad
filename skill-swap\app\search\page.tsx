"use client"

import ProtectedRoute from "@/components/ProtectedRoute"
import TopNavBar from "@/components/dashboard/TopNavBar"
import { useState, useMemo } from "react"
import { Search, Star, MapPin, Clock, Users, ChevronRight, Sparkles } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const SAMPLE_USERS = [
  {
    id: 1,
    name: "<PERSON><PERSON> <PERSON>",
    rating: 4.9,
    skills: [
      { name: "Machine Learning", color: "from-[#4F46E5] to-[#7C3AED]" },
      { name: "Python", color: "from-[#2563EB] to-[#7C3AED]" },
      { name: "Data Science", color: "from-[#7C3AED] to-[#9333EA]" }
    ],
    location: "Bangalore, India",
    availability: "Weekday Evenings",
    image: "/experts/priya.jpg",
    level: "Expert",
    students: 1580,
    gradient: "from-[#4F46E5] to-[#7C3AED]",
    languages: ["English", "Hindi", "Kannada"]
  },
  {
    id: 2,
    name: "<PERSON>",
    rating: 4.8,
    skills: [
      { name: "Photography", color: "from-[#F59E0B] to-[#EF4444]" },
      { name: "Video Editing", color: "from-[#EF4444] to-[#DC2626]" },
      { name: "Adobe Suite", color: "from-[#DC2626] to-[#B91C1C]" }
    ],
    location: "New York, USA",
    availability: "Flexible Hours",
    image: "/experts/marcus.jpg",
    level: "Professional",
    students: 856,
    gradient: "from-[#F59E0B] to-[#EF4444]",
    languages: ["English", "Spanish"]
  },
  {
    id: 3,
    name: "Rajesh Kumar",
    rating: 4.7,
    skills: [
      { name: "Web Development", color: "from-[#10B981] to-[#059669]" },
      { name: "React", color: "from-[#059669] to-[#047857]" },
      { name: "Node.js", color: "from-[#047857] to-[#065F46]" }
    ],
    location: "Mumbai, India",
    availability: "Weekends",
    image: "/experts/rajesh.jpg",
    level: "Advanced",
    students: 932,
    gradient: "from-[#10B981] to-[#3B82F6]",
    languages: ["English", "Hindi", "Marathi"]
  }
]

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [skillLevel, setSkillLevel] = useState("all")
  const [availability, setAvailability] = useState("any")
  const [location, setLocation] = useState("all")

  // Filter users based on search and filters
  const filteredUsers = useMemo(() => {
    return SAMPLE_USERS.filter(user => {
      const matchesSearch = searchQuery === "" || 
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.skills.some(skill => skill.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        user.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.languages.some(lang => lang.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesLevel = skillLevel === "all" || user.level.toLowerCase() === skillLevel.toLowerCase()
      
      const matchesAvailability = availability === "any" || 
        user.availability.toLowerCase().includes(availability.toLowerCase())
      
      const matchesLocation = location === "all" || 
        (location === "local" && user.location.includes("India")) ||
        (location === "global" && !user.location.includes("India"))

      return matchesSearch && matchesLevel && matchesAvailability && matchesLocation
    })
  }, [searchQuery, skillLevel, availability, location])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <TopNavBar />
        
        <main className="pt-24">
          <div className="container mx-auto px-4 py-8">
            {/* Hero Section */}
            <div className="text-center mb-16">
              <h1 className="text-5xl font-bold bg-gradient-to-r from-[#4F46E5] to-[#B10DC9] bg-clip-text text-transparent mb-4">
                Find Your Perfect Skill Match
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Connect with expert instructors and learn the skills you've always wanted
              </p>
            </div>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="max-w-3xl mx-auto mb-12">
              <div className="flex shadow-lg rounded-2xl">
                <div className="relative flex-grow">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
                  <Input
                    type="text"
                    placeholder="Search by name, skills, location, or language..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-12 h-14 text-lg rounded-l-2xl border-0 ring-0 focus:ring-0 focus:border-0"
                  />
                </div>
                <Button type="submit" className="h-14 px-8 rounded-r-2xl bg-gradient-to-r from-[#4F46E5] to-[#B10DC9] text-white hover:opacity-90 transition-opacity">
                  <Search className="h-5 w-5 mr-2" />
                  <span>Search</span>
                </Button>
              </div>
            </form>

            {/* Filters Panel */}
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-12 max-w-5xl mx-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-[#B10DC9]" />
                  Smart Filters
                </h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="skill-level" className="block text-sm font-medium text-gray-700 mb-2">
                    Skill Level
                  </label>
                  <Select value={skillLevel} onValueChange={setSkillLevel}>
                    <SelectTrigger id="skill-level" className="h-12">
                      <SelectValue placeholder="Select skill level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                      <SelectItem value="expert">Expert</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label htmlFor="availability" className="block text-sm font-medium text-gray-700 mb-2">
                    Availability
                  </label>
                  <Select value={availability} onValueChange={setAvailability}>
                    <SelectTrigger id="availability" className="h-12">
                      <SelectValue placeholder="Select availability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any">Any Time</SelectItem>
                      <SelectItem value="morning">Morning</SelectItem>
                      <SelectItem value="evening">Evening</SelectItem>
                      <SelectItem value="weekday">Weekdays</SelectItem>
                      <SelectItem value="weekend">Weekends</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <Select value={location} onValueChange={setLocation}>
                    <SelectTrigger id="location" className="h-12">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Locations</SelectItem>
                      <SelectItem value="local">India</SelectItem>
                      <SelectItem value="global">International</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Results Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredUsers.map((user) => (
                <div key={user.id} className="group">
                  <div className="relative h-[420px] bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02]">
                    {/* Gradient Background */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${user.gradient} opacity-90`} />

                    {/* Content */}
                    <div className="relative h-full p-6 flex flex-col text-white">
                      {/* Header */}
                      <div className="flex items-center gap-4 mb-6">
                        <div className="relative w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                          <span className="text-2xl font-bold">{user.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold">{user.name}</h3>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-300" />
                              <span className="ml-1 font-medium">{user.rating}</span>
                            </div>
                            <span className="text-white/60">•</span>
                            <span className="text-white/90">{user.level}</span>
                          </div>
                        </div>
                      </div>

                      {/* Skills */}
                      <div className="space-y-4 mb-6">
                        <h4 className="font-semibold">Expert in</h4>
                        <div className="flex flex-wrap gap-2">
                          {user.skills.map((skill) => (
                            <span
                              key={skill.name}
                              className="px-3 py-1 rounded-full text-sm font-medium bg-white/10 hover:bg-white/20 transition-colors"
                            >
                              {skill.name}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Info */}
                      <div className="space-y-3 mb-6">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span className="text-sm">{user.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span className="text-sm">{user.availability}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          <span className="text-sm">{user.students} students</span>
                        </div>
                      </div>

                      {/* Action Button */}
                      <div className="mt-auto">
                        <Button className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/20 transition-colors">
                          Connect
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-16">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-semibold text-gray-700 mb-2">No matches found</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  Try adjusting your search terms or filters to find more results
                </p>
              </div>
            )}
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
# SwapUP - Troubleshooting Guide

This document covers common issues and their solutions for the SwapUP project.

## 🚨 Critical Build Issues

### Build Process Freezing

**Symptoms:**
- `npm run dev` hangs indefinitely
- Build process stops at compilation
- No error messages, just freezes

**Root Causes:**
1. Corrupted `.next` cache
2. Merge conflicts in files
3. Circular dependencies
4. Memory issues
5. Node.js version conflicts

**Solutions:**

#### Solution 1: Complete Clean Reset
```bash
# Kill all Node processes
taskkill /f /im node.exe  # Windows
# or
pkill -f node            # macOS/Linux

# Remove all build artifacts
rm -rf .next
rm -rf node_modules
rm -rf package-lock.json

# Reinstall everything
npm install
npm run dev
```

#### Solution 2: Fix Merge Conflicts
```bash
# Check for merge conflict markers
grep -r "<<<<<<< HEAD" .
grep -r "=======" .
grep -r ">>>>>>> " .

# Fix any files with conflicts manually
```

#### Solution 3: Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run dev

# Or add to package.json scripts:
"dev": "NODE_OPTIONS='--max-old-space-size=4096' next dev"
```

### Compilation Errors

**Common TypeScript Errors:**

#### Error: "Cannot find module"
```bash
# Solution: Check imports and install missing packages
npm install @types/node @types/react @types/react-dom
```

#### Error: "Unexpected token"
```bash
# Usually indicates syntax errors or merge conflicts
# Check the file mentioned in the error for:
# - Missing semicolons
# - Unclosed brackets
# - Merge conflict markers (<<<<<<, ======, >>>>>>)
```

#### Error: "Module not found: Can't resolve"
```bash
# Check if the file exists
# Verify import paths are correct
# Ensure file extensions are included if needed
```

## 🔧 Database Issues

### Prisma Connection Problems

**Error: "Can't reach database server"**
```bash
# Solution: Reset database
rm prisma/dev.db
npm run db:push
npm run db:generate
```

**Error: "Schema drift detected"**
```bash
# Solution: Sync schema
npm run db:push
```

**Error: "Prisma Client not generated"**
```bash
# Solution: Generate client
npm run db:generate
```

### Database Migration Issues

```bash
# Reset database completely
rm prisma/dev.db
rm -rf prisma/migrations
npm run db:push
```

## 🔐 Authentication Issues

### JWT Token Problems

**Issue: Login not working**
```bash
# Check environment variables
echo $JWT_SECRET

# Verify API routes
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"dummyPass"}'
```

**Issue: Token expired or invalid**
```javascript
// Clear localStorage in browser console
localStorage.removeItem('auth-token')
// Then try logging in again
```

### Route Protection Issues

**Issue: Protected routes not working**
1. Check if `ProtectedRoute` component is wrapping the page
2. Verify authentication context is provided in layout
3. Check browser console for errors

```tsx
// Correct usage:
export default function ProtectedPage() {
  return (
    <ProtectedRoute>
      <div>Protected content</div>
    </ProtectedRoute>
  )
}
```

## 🎨 Styling Issues

### Tailwind CSS Not Working

**Issue: Classes not applying**
```bash
# Check tailwind.config.js includes all paths
# Restart development server
npm run dev
```

**Issue: Custom colors not working**
```javascript
// Verify colors are defined in tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#B10DC9',
        secondary: '#4F46E5'
      }
    }
  }
}
```

### Component Styling Issues

**Issue: Components not styled correctly**
1. Check if className props are passed correctly
2. Verify component imports
3. Use browser dev tools to inspect styles

## 🌐 API Issues

### API Routes Not Working

**Issue: 404 on API calls**
```bash
# Verify API route files exist in app/api/
# Check file naming: route.ts (not index.ts)
# Ensure proper HTTP methods are exported
```

**Issue: CORS errors**
```javascript
// Add CORS headers in API routes
export async function POST(request) {
  const response = NextResponse.json(data)
  response.headers.set('Access-Control-Allow-Origin', '*')
  return response
}
```

### Network Request Issues

**Issue: Fetch requests failing**
```javascript
// Add error handling
try {
  const response = await fetch('/api/endpoint')
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  const data = await response.json()
} catch (error) {
  console.error('Fetch error:', error)
}
```

## 📱 UI/UX Issues

### Responsive Design Problems

**Issue: Layout breaks on mobile**
```css
/* Use responsive Tailwind classes */
<div className="w-full md:w-1/2 lg:w-1/3">
<div className="text-sm md:text-base lg:text-lg">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

### Component Rendering Issues

**Issue: Components not rendering**
1. Check for JavaScript errors in console
2. Verify component imports and exports
3. Check if required props are passed

**Issue: Hydration errors**
```javascript
// Ensure server and client render the same content
// Use useEffect for client-only code
useEffect(() => {
  // Client-only code here
}, [])
```

## 🚀 Performance Issues

### Slow Build Times

**Solutions:**
```bash
# Use SWC compiler (already enabled in Next.js 13+)
# Reduce bundle size by checking imports
npm run build -- --analyze

# Clear cache
rm -rf .next
```

### Runtime Performance Issues

**Solutions:**
1. Use React.memo for expensive components
2. Implement proper loading states
3. Use Next.js Image component for images
4. Implement code splitting

```tsx
// Example: Lazy loading components
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>
})
```

## 🔍 Debugging Techniques

### Browser Developer Tools

1. **Console Tab**: Check for JavaScript errors
2. **Network Tab**: Monitor API requests
3. **Application Tab**: Check localStorage/sessionStorage
4. **Elements Tab**: Inspect HTML and CSS

### React Developer Tools

```bash
# Install React DevTools browser extension
# Use to inspect component state and props
```

### Server-Side Debugging

```javascript
// Add console.log statements in API routes
export async function POST(request) {
  console.log('API called with:', await request.json())
  // ... rest of code
}
```

### Database Debugging

```bash
# Open Prisma Studio to inspect database
npm run db:studio

# Check database queries
# Enable Prisma query logging in schema.prisma
generator client {
  provider = "prisma-client-js"
  log      = ["query", "info", "warn", "error"]
}
```

## 🆘 Emergency Recovery

### Complete Project Reset

If everything is broken and you need to start fresh:

```bash
# 1. Backup your changes
git stash
git branch backup-$(date +%Y%m%d)

# 2. Reset to last working commit
git reset --hard HEAD~1

# 3. Clean everything
rm -rf .next node_modules package-lock.json prisma/dev.db

# 4. Reinstall
npm install
npm run db:push
npm run db:generate
npm run dev
```

### Rollback Database

```bash
# If database is corrupted
rm prisma/dev.db
npm run db:push

# This will recreate the database with the current schema
```

## 📞 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Search existing GitHub issues**
3. **Check browser console for errors**
4. **Try the emergency recovery steps**

### When Reporting Issues

Include:
- **Error messages** (full stack trace)
- **Steps to reproduce**
- **Environment details** (Node.js version, OS)
- **Browser and version**
- **Code snippets** (if relevant)

### Useful Commands for Diagnostics

```bash
# System information
node --version
npm --version
npx next --version

# Project information
npm list --depth=0
npm run build 2>&1 | tee build.log

# Check for common issues
npm audit
npm outdated
```

## 🔧 Preventive Measures

### Regular Maintenance

```bash
# Weekly cleanup
rm -rf .next
npm install

# Monthly updates (be careful)
npm update
npm audit fix
```

### Best Practices

1. **Commit frequently** with meaningful messages
2. **Test before committing** major changes
3. **Keep dependencies updated** regularly
4. **Use TypeScript** for better error catching
5. **Follow the project structure** consistently

### Code Quality

```bash
# Set up pre-commit hooks
npm install --save-dev husky lint-staged

# Add to package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": ["eslint --fix", "git add"]
  }
}
```

---

**Remember: When in doubt, clean everything and start fresh! 🧹**
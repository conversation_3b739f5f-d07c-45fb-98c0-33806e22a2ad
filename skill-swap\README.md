# SwapUP - Skill Exchange Platform

A modern, full-stack skill-sharing platform built with Next.js 14, TypeScript, Prisma, and SQLite. Connect with experts, learn new skills, and share your knowledge with a global community.

## 🌟 Features

### Core Functionality
- **User Authentication** - Secure JWT-based login/signup system
- **Skill Matching** - AI-powered skill matching and recommendations
- **Real-time Messaging** - Chat with other users and mentors
- **Video Reels** - Share and discover skill tutorials
- **Advanced Search** - Find users by skills, location, and availability
- **Dashboard** - Personalized user dashboard with analytics
- **Route Protection** - Secure protected routes for authenticated users

### Technical Features
- **Modern UI/UX** - Built with Tailwind CSS and Radix UI components
- **Responsive Design** - Mobile-first approach with full responsiveness
- **Database Integration** - Prisma ORM with SQLite database
- **API Routes** - RESTful API endpoints with Next.js App Router
- **Type Safety** - Full TypeScript implementation
- **Authentication** - JWT tokens with localStorage persistence
- **Real-time Features** - Ready for WebSocket integration
- **Accessibility** - WCAG compliant components and features

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **npm** or **yarn** package manager
- **Git** for version control

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd skill-swap
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

4. **Initialize the database**
   ```bash
   npm run db:push
   npm run db:generate
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
skill-swap/
├── app/                          # Next.js App Router
│   ├── api/                      # API Routes
│   │   └── auth/                 # Authentication endpoints
│   │       ├── login/route.ts    # Login API
│   │       └── signup/route.ts   # Signup API
│   ├── explore/                  # Explore page
│   ├── login/                    # Login page
│   ├── messenger/                # Messaging interface
│   ├── reels/                    # Video reels page
│   ├── search/                   # Search functionality
│   ├── signup/                   # User registration
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Home page
├── components/                   # Reusable components
│   ├── dashboard/                # Dashboard components
│   │   ├── TopNavBar.tsx         # Navigation bar
│   │   └── UserProfileDropdown.tsx # User menu
│   ├── ui/                       # UI components (Radix UI)
│   ├── ClientLayout.tsx          # Client-side layout wrapper
│   ├── Header.tsx                # Public header
│   ├── ProtectedRoute.tsx        # Route protection HOC
│   └── PublicNavBar.tsx          # Public navigation
├── context/                      # React Context
│   └── auth-context.tsx          # Authentication context
├── lib/                          # Utility libraries
│   ├── auth.ts                   # Authentication utilities
│   ├── prisma.ts                 # Prisma client
│   └── utils.ts                  # General utilities
├── prisma/                       # Database
│   ├── schema.prisma             # Database schema
│   └── dev.db                    # SQLite database file
├── public/                       # Static assets
├── styles/                       # Additional styles
├── .env.local                    # Environment variables
├── .env.example                  # Environment template
├── package.json                  # Dependencies and scripts
├── tailwind.config.js            # Tailwind configuration
├── tsconfig.json                 # TypeScript configuration
└── next.config.mjs               # Next.js configuration
```

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Database
DATABASE_URL="file:./dev.db"

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Dummy Login Credentials (for development)
DUMMY_EMAIL=<EMAIL>
DUMMY_PASSWORD=dummyPass

# API Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Optional API Keys
NEXT_PUBLIC_GEMINI_API_KEY=your-gemini-api-key-here
JOB_API_KEY=your-job-api-key-here
```

### Database Schema

The application uses Prisma with SQLite. Key models include:

- **User** - User accounts and profiles
- **Skill** - Available skills in the platform
- **UserSkill** - User-skill relationships with proficiency levels
- **Message** - Direct messaging between users
- **Post** - User posts and reels
- **Like** - Post interactions
- **Comment** - Post comments

## 🎯 Usage Guide

### Authentication

#### Login
- **Dummy Account**: `<EMAIL>` / `dummyPass`
- **Real Accounts**: Create via signup form
- **JWT Tokens**: Stored in localStorage with 7-day expiration

#### Signup
- Email validation
- Password requirements (minimum 6 characters)
- Automatic login after successful registration

### Navigation

#### Public Routes (No Authentication Required)
- `/` - Landing page with features overview
- `/login` - User login
- `/signup` - User registration

#### Protected Routes (Authentication Required)
- `/` - Dashboard (when logged in)
- `/messenger` - Real-time messaging
- `/reels` - Video content sharing
- `/explore` - Skill discovery
- `/search` - Advanced user search

### Features Deep Dive

#### Dashboard
- **Personal Stats**: Skills learned, taught, achievements
- **Recent Activity**: Learning progress and teaching history
- **Quick Actions**: Find mentors, offer skills, browse courses
- **Skill of the Week**: Featured learning content

#### Messaging System
- **Real-time Chat**: Direct messaging between users
- **Conversation Management**: Organized chat history
- **User Search**: Find and connect with other users
- **Message Status**: Read/unread indicators

#### Reels Platform
- **Video Sharing**: Upload and share skill tutorials
- **Interactive Features**: Likes, comments, shares
- **Content Discovery**: Browse trending skill content
- **Creator Tools**: Video management and analytics

#### Search & Discovery
- **Advanced Filters**: Skill level, availability, location
- **Smart Matching**: AI-powered user recommendations
- **Skill Categories**: Organized skill browsing
- **User Profiles**: Detailed instructor profiles

## 🛠 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server
npm run lint            # Run ESLint

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema to database
npm run db:studio       # Open Prisma Studio

# Optional Services
npm run ml-service      # Start ML recommendation service
npm run dev-with-ml     # Start with ML service
```

### Code Style & Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Code linting and formatting
- **Prettier**: Code formatting (if configured)
- **Tailwind CSS**: Utility-first styling
- **Component Structure**: Functional components with hooks

### Adding New Features

1. **Create Components**: Add to `components/` directory
2. **Add Routes**: Create in `app/` directory
3. **Database Changes**: Update `prisma/schema.prisma`
4. **API Endpoints**: Add to `app/api/` directory
5. **Types**: Define in TypeScript interfaces

## 🔒 Security

### Authentication Security
- **JWT Tokens**: Secure token-based authentication
- **Password Hashing**: bcrypt for password security
- **Route Protection**: Server-side route validation
- **CORS**: Configured for secure API access

### Data Protection
- **Input Validation**: Zod schema validation
- **SQL Injection**: Prisma ORM protection
- **XSS Prevention**: React built-in protections
- **Environment Variables**: Sensitive data protection

## 🚀 Deployment

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm run start
```

### Environment Setup

1. **Database**: Set up production database (PostgreSQL recommended)
2. **Environment Variables**: Configure production values
3. **JWT Secret**: Use strong, unique secret key
4. **API Keys**: Add production API keys if needed

### Deployment Platforms

- **Vercel** (Recommended for Next.js)
- **Netlify**
- **Railway**
- **DigitalOcean App Platform**

## 🧪 Testing

### Test Accounts

For development and testing:

```
Email: <EMAIL>
Password: dummyPass
```

### API Testing

Test authentication endpoints:

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"dummyPass"}'

# Signup
curl -X POST http://localhost:3000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'
```

## 🐛 Troubleshooting

### Common Issues

#### Build Freezing
```bash
# Clear cache and reinstall
rm -rf .next node_modules
npm install
npm run dev
```

#### Database Issues
```bash
# Reset database
npm run db:push
npm run db:generate
```

#### Authentication Problems
- Check JWT_SECRET in environment variables
- Verify localStorage is accessible
- Ensure API routes are working

#### Styling Issues
- Verify Tailwind CSS is properly configured
- Check component imports
- Ensure CSS files are loaded

### Performance Optimization

- **Image Optimization**: Use Next.js Image component
- **Code Splitting**: Automatic with Next.js App Router
- **Bundle Analysis**: Use `@next/bundle-analyzer`
- **Caching**: Implement proper caching strategies

## 📚 API Documentation

### Authentication Endpoints

#### POST `/api/auth/login`
Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt-token-here",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name"
  }
}
```

#### POST `/api/auth/signup`
Create a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "User Name"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt-token-here",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name"
  }
}
```

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes**: Implement your feature
4. **Test thoroughly**: Ensure everything works
5. **Commit changes**: `git commit -m 'Add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Code Guidelines

- Follow TypeScript best practices
- Use meaningful component and variable names
- Add comments for complex logic
- Ensure responsive design
- Test on multiple browsers

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** - React framework
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component library
- **Prisma** - Database toolkit
- **Lucide React** - Icon library

## 📞 Support

For support and questions:

- **Issues**: Create a GitHub issue
- **Documentation**: Check this README
- **Community**: Join our Discord server (if available)

---

**Built with ❤️ for the skill-sharing community**
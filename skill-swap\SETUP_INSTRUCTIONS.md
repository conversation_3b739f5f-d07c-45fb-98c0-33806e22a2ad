# SkillSwap Application Setup Instructions

## Prerequisites

1. **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
2. **Python** (v3.8 or higher) - [Download here](https://python.org/)
3. **XAMPP** (for MySQL and PHP) - [Download here](https://www.apachefriends.org/)

## Quick Setup

### Option 1: Automated Setup (Recommended)
Run the PowerShell setup script:
```powershell
.\setup_and_run.ps1
```

### Option 2: Manual Setup

#### 1. Database Setup
1. Start XAMPP Control Panel
2. Start Apache and MySQL services
3. Open phpMyAdmin (http://localhost/phpmyadmin)
4. Import `database_setup.sql` file to create the database and tables

#### 2. Frontend Setup
```bash
npm install
npm run dev
```
Frontend will run on: http://localhost:3000

#### 3. Python ML Service Setup
```bash
cd ml_service
python -m venv venv
.\venv\Scripts\Activate.ps1  # Windows
# or
source venv/bin/activate     # Linux/Mac
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```
ML Service will run on: http://localhost:8000

#### 4. PHP Backend Setup
1. Copy `skillswap-api` folder to XAMPP's `htdocs` directory
2. Or configure Apache to serve from current directory
3. Backend will be available at: http://localhost/skillswap-api

## Environment Configuration

Create `.env.local` file in the root directory:
```env
NEXT_PUBLIC_API_URL=http://localhost/skillswap-api
NEXT_PUBLIC_ML_API_URL=http://localhost:8000
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here
```

## Testing the Setup

1. **Frontend**: Visit http://localhost:3000
2. **ML Service**: Visit http://localhost:8000/docs (FastAPI docs)
3. **PHP Backend**: Visit http://localhost/skillswap-api/test_db.php
4. **Database**: Check phpMyAdmin for tables

## Default Login Credentials

Use these test accounts:
- Email: <EMAIL>, Password: password
- Email: <EMAIL>, Password: password
- Email: <EMAIL>, Password: password
- Email: <EMAIL>, Password: password

## Troubleshooting

### Common Issues:

1. **Port conflicts**: Change ports in the respective config files
2. **Database connection**: Verify MySQL is running and credentials are correct
3. **CORS errors**: Ensure all services are running on correct ports
4. **Python dependencies**: Make sure virtual environment is activated

### Service URLs:
- Frontend: http://localhost:3000
- ML Service: http://localhost:8000
- PHP Backend: http://localhost/skillswap-api
- Database: http://localhost/phpmyadmin

## Features Available

1. **User Authentication**: Login/Signup system
2. **Skill Management**: Add/remove skills, set proficiency levels
3. **Job Recommendations**: ML-powered job matching
4. **Messaging System**: Chat with other users
5. **Reels**: Video content sharing
6. **Search**: Find users by skills
7. **Dashboard**: Personal profile and analytics

## API Endpoints

### PHP Backend (`/skillswap-api/`)
- `auth.php` - Authentication
- `skills.php` - Skill management
- `search.php` - User search
- `messages.php` - Messaging
- `reels.php` - Video content
- `matches.php` - Skill matching

### Python ML Service (`http://localhost:8000/`)
- `/recommend-jobs` - Job recommendations
- `/docs` - API documentation

## Development Notes

- Frontend uses Next.js 14 with TypeScript
- Backend uses PHP with MySQL
- ML service uses FastAPI with Python
- UI components from Radix UI and Tailwind CSS
- Authentication uses JWT tokens
- Real-time features ready for WebSocket integration

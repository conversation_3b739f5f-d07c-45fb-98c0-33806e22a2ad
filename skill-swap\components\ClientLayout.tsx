"use client"

import { AuthProvider } from "@/context/auth-context"
import AccessibilitySettings from "@/components/AccessibilitySettings"
import { useEffect, useState } from "react"

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isMessagePage, setIsMessagePage] = useState(false);

  useEffect(() => {
    setIsMessagePage(window.location.pathname.includes("/messenger/newMessagePage"));
  }, []);

  return (
    <>
      {isMessagePage ? (
        children
      ) : (
        <AuthProvider>
          {children}
          <AccessibilitySettings />
        </AuthProvider>
      )}
    </>
  );
}

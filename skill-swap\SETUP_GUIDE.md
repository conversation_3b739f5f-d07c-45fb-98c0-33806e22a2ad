# SwapUP - Complete Setup Guide

This guide will walk you through setting up the SwapUP skill exchange platform from scratch.

## 🎯 Prerequisites

### Required Software

1. **Node.js (v18+)**
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **npm (comes with Node.js)**
   - Verify installation: `npm --version`

3. **Git**
   - Download from [git-scm.com](https://git-scm.com/)
   - Verify installation: `git --version`

### Optional but Recommended

- **VS Code** - Code editor with great TypeScript support
- **Prisma Studio** - Database GUI (included in project)

## 🚀 Step-by-Step Installation

### Step 1: Clone and Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd skill-swap

# Install dependencies
npm install
```

### Step 2: Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local
```

Edit `.env.local` with your settings:

```env
# Database
DATABASE_URL="file:./dev.db"

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Dummy Login Credentials
DUMMY_EMAIL=<EMAIL>
DUMMY_PASSWORD=dummyPass

# API Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Optional: Add your API keys
# NEXT_PUBLIC_GEMINI_API_KEY=your-gemini-api-key
```

### Step 3: Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Create and sync database
npm run db:push
```

### Step 4: Start Development Server

```bash
# Start the application
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## 🔧 Development Workflow

### Daily Development

```bash
# Start development server
npm run dev

# In another terminal, open database GUI (optional)
npm run db:studio
```

### Making Database Changes

```bash
# 1. Edit prisma/schema.prisma
# 2. Push changes to database
npm run db:push

# 3. Regenerate client
npm run db:generate
```

### Adding New Dependencies

```bash
# Add a new package
npm install package-name

# Add a dev dependency
npm install -D package-name
```

## 🎨 UI Components

### Using Existing Components

```tsx
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

function MyComponent() {
  return (
    <div>
      <Input placeholder="Enter text" />
      <Button>Click me</Button>
    </div>
  )
}
```

### Creating New Components

```tsx
// components/MyComponent.tsx
"use client"

import { useState } from "react"

interface MyComponentProps {
  title: string
  onAction?: () => void
}

export default function MyComponent({ title, onAction }: MyComponentProps) {
  const [isActive, setIsActive] = useState(false)

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-xl font-bold">{title}</h2>
      <button 
        onClick={() => setIsActive(!isActive)}
        className="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Toggle
      </button>
    </div>
  )
}
```

## 🔐 Authentication System

### How Authentication Works

1. **Login Process**:
   - User submits email/password
   - Server validates credentials
   - JWT token generated and returned
   - Token stored in localStorage
   - User redirected to dashboard

2. **Route Protection**:
   - `ProtectedRoute` component checks authentication
   - Redirects to login if not authenticated
   - Shows loading spinner during check

3. **Token Management**:
   - Tokens expire after 7 days
   - Automatic logout on token expiry
   - Token verification on each protected route

### Using Authentication in Components

```tsx
import { useAuth } from "@/context/auth-context"

function MyComponent() {
  const { isAuthenticated, user, login, logout } = useAuth()

  if (!isAuthenticated) {
    return <div>Please log in</div>
  }

  return (
    <div>
      <h1>Welcome, {user?.name}!</h1>
      <button onClick={logout}>Logout</button>
    </div>
  )
}
```

## 🗄️ Database Operations

### Using Prisma Client

```tsx
// In API routes or server components
import { prisma } from "@/lib/prisma"

// Create a user
const user = await prisma.user.create({
  data: {
    email: "<EMAIL>",
    name: "John Doe",
    password: hashedPassword
  }
})

// Find users with skills
const users = await prisma.user.findMany({
  include: {
    skills: {
      include: {
        skill: true
      }
    }
  }
})

// Update user
await prisma.user.update({
  where: { id: userId },
  data: { name: "New Name" }
})
```

### Database Schema Overview

```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  
  skills    UserSkill[]
  messages  Message[]
  posts     Post[]
}

model Skill {
  id          String @id @default(cuid())
  name        String @unique
  category    String
  description String?
  
  userSkills UserSkill[]
}

model UserSkill {
  id         String  @id @default(cuid())
  userId     String
  skillId    String
  level      String  // beginner, intermediate, advanced, expert
  isTeaching Boolean @default(false)
  isLearning Boolean @default(false)
  
  user  User  @relation(fields: [userId], references: [id])
  skill Skill @relation(fields: [skillId], references: [id])
}
```

## 🎨 Styling Guide

### Tailwind CSS Classes

```tsx
// Common patterns
<div className="flex items-center justify-between">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
<div className="bg-white rounded-lg shadow-md p-6">
<button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">

// Responsive design
<div className="w-full md:w-1/2 lg:w-1/3">
<div className="text-sm md:text-base lg:text-lg">

// Colors (using project theme)
<div className="bg-[#B10DC9] text-white">
<div className="text-[#4F46E5]">
```

### Custom Components with Styling

```tsx
// Using className prop for customization
interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  className?: string
}

function CustomButton({ children, variant = 'primary', className = '' }: ButtonProps) {
  const baseClasses = "px-4 py-2 rounded font-medium transition-colors"
  const variantClasses = {
    primary: "bg-[#B10DC9] hover:bg-[#8a0a9b] text-white",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-800"
  }

  return (
    <button className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </button>
  )
}
```

## 🔄 State Management

### Using React Context

```tsx
// Creating a context
import { createContext, useContext, useState } from "react"

interface AppContextType {
  theme: 'light' | 'dark'
  setTheme: (theme: 'light' | 'dark') => void
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<'light' | 'dark'>('light')

  return (
    <AppContext.Provider value={{ theme, setTheme }}>
      {children}
    </AppContext.Provider>
  )
}

export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within AppProvider')
  }
  return context
}
```

### Local State Management

```tsx
import { useState, useEffect } from "react"

function MyComponent() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const response = await fetch('/api/data')
        const result = await response.json()
        setData(result)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  )
}
```

## 🚀 Building for Production

### Production Build

```bash
# Build the application
npm run build

# Test production build locally
npm run start
```

### Environment Variables for Production

```env
# Production .env.local
DATABASE_URL="your-production-database-url"
JWT_SECRET="your-super-secure-production-secret"
NEXT_PUBLIC_APP_URL="https://your-domain.com"
NEXT_PUBLIC_API_URL="https://your-domain.com/api"
```

### Deployment Checklist

- [ ] Update environment variables
- [ ] Set up production database
- [ ] Configure domain and SSL
- [ ] Test all functionality
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy

## 🐛 Common Issues and Solutions

### Build Issues

**Issue**: Build freezes or fails
```bash
# Solution: Clean and reinstall
rm -rf .next node_modules package-lock.json
npm install
npm run dev
```

**Issue**: TypeScript errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Fix common issues
npm run lint
```

### Database Issues

**Issue**: Database connection fails
```bash
# Reset database
rm prisma/dev.db
npm run db:push
```

**Issue**: Schema changes not reflected
```bash
# Regenerate Prisma client
npm run db:generate
npm run db:push
```

### Authentication Issues

**Issue**: Login not working
- Check JWT_SECRET in environment variables
- Verify API routes are accessible
- Check browser console for errors
- Clear localStorage and try again

**Issue**: Protected routes not working
- Verify ProtectedRoute component is wrapping pages
- Check authentication context is provided
- Ensure token is valid and not expired

### Styling Issues

**Issue**: Tailwind classes not working
- Verify tailwind.config.js includes all paths
- Check if CSS is properly imported
- Restart development server

**Issue**: Components not styled correctly
- Check component imports
- Verify className props are passed correctly
- Use browser dev tools to inspect styles

## 📚 Learning Resources

### Next.js
- [Next.js Documentation](https://nextjs.org/docs)
- [Next.js Learn Course](https://nextjs.org/learn)

### TypeScript
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

### Prisma
- [Prisma Documentation](https://www.prisma.io/docs)
- [Prisma Examples](https://github.com/prisma/prisma-examples)

### Tailwind CSS
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Tailwind UI Components](https://tailwindui.com/)

## 🤝 Getting Help

### Community Resources
- GitHub Issues for bug reports
- Stack Overflow for technical questions
- Discord/Slack for real-time help

### Debugging Tips
1. Check browser console for errors
2. Use React Developer Tools
3. Check network tab for API issues
4. Use Prisma Studio for database inspection
5. Add console.log statements for debugging

---

**Happy coding! 🚀**